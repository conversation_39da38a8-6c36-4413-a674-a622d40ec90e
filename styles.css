body {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
}

.container {
    padding: 20px;
}

h2 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
    text-align: center;
}

p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 12px;
    text-align: center;
}

#urlInput {
    width: 100%;
    min-height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    resize: vertical;
    box-sizing: border-box;
}

#urlInput:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-primary:active {
    transform: scale(0.95);
}

.btn-secondary {
    background-color: #f44336;
    color: white;
}

.btn-secondary:hover {
    background-color: #da190b;
}

.btn-secondary:active {
    transform: scale(0.95);
}

.status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    min-height: 20px;
}

.status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 分隔线 */
.separator {
    height: 1px;
    background-color: #ddd;
    margin: 20px 0;
}

/* 文本处理区域 */
h3 {
    margin: 15px 0 5px 0;
    color: #333;
    font-size: 16px;
}

#textInput {
    width: 100%;
    min-height: 120px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    resize: vertical;
    box-sizing: border-box;
    margin-bottom: 15px;
}

#textInput:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
}

/* 处理结果区域 */
.processed-result {
    margin-top: 15px;
    padding: 15px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: none;
}

.processed-result.show {
    display: block;
}

.processed-result h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.result-content {
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.4;
    white-space: pre-wrap;
}