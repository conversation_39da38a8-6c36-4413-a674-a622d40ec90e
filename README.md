# 链接批量打开器 Chrome插件

## 功能简介
这是一个Chrome浏览器扩展，可以批量打开从Excel复制的链接地址。

## 主要功能
- 从Excel复制一列链接地址到输入框
- 一键批量打开所有链接
- 自动清理输入框内容
- 保存上次输入的链接
- 支持快捷键操作（Ctrl+Enter快速打开）
- **文本处理工具**：处理Excel列内容，将"-"替换为空格，过滤纯数字内容

## 安装方法

1. **下载扩展文件**
   - 下载整个`LinkOpen`文件夹到本地

2. **加载扩展**
   - 打开Chrome浏览器
   - 在地址栏输入：`chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择`LinkOpen`文件夹

3. **使用扩展**
   - 安装成功后，浏览器右上角会出现扩展图标
   - 点击图标会在浏览器右侧打开侧边栏

## 使用方法

插件采用标签页设计，包含两个主要功能：

### 链接打开功能

1. **切换到链接打开标签**
   - 点击扩展图标打开侧边栏
   - 默认显示"链接打开"标签页

2. **复制和粘贴链接**
   - 在Excel中选中一列链接地址，复制（Ctrl+C）
   - 将复制的链接粘贴到输入框中（Ctrl+V）

3. **批量打开**
   - 点击"打开全部链接"按钮
   - 所有链接将在新标签页中打开

4. **清理内容**
   - 点击"清理内容"按钮清空输入框

### 文本处理功能

1. **切换到文本处理标签**
   - 点击"文本处理"标签页

2. **输入文本**
   - 在文本处理区域粘贴Excel列内容

3. **处理文本**
   - 点击"处理文本"按钮
   - 自动将内容中的"-"替换为空格
   - 自动过滤掉纯数字内容

4. **查看结果**
   - 处理后的内容会在下方显示
   - 每个词条一行显示，右侧有操作按钮

5. **词条操作**
   - 📋 复制按钮：点击复制单个词条到剪贴板
   - ✕ 删除按钮：点击删除不需要的词条

6. **清理文本**
   - 点击"清理文本"按钮清空文本输入框

## 注意事项

- 链接可以是完整的URL（https://example.com）或简写形式（example.com）
- 每行一个链接，支持从Excel直接复制的格式
- 为避免被浏览器阻止，链接会间隔100毫秒依次打开
- 插件会自动保存上次输入的链接和文本，下次打开时自动恢复
- 使用侧边栏界面，提供更大的操作空间
- 标签页设计，功能分类清晰，界面简洁
- 文本处理功能支持快捷键（Ctrl+Enter快速处理）
- 词条列表支持单独复制和删除操作
- 文本处理示例：
  - 输入：`word-one`、`word-two`、`12345`、`text-content`
  - 输出：`word one`、`word two`、`text content`（数字`12345`被过滤）
  - 每个词条右侧有复制📋和删除✕按钮

## 技术说明

- 基于Chrome Extension Manifest V3开发
- 使用纯HTML/CSS/JavaScript实现
- 无需额外权限，仅使用基本的标签页操作权限

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的链接批量打开功能
- 添加界面美化和用户体验优化