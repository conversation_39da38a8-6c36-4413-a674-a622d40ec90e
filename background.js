// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  // 打开侧边栏
  await chrome.sidePanel.open({ tabId: tab.id });
});

// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(() => {
  console.log('链接批量打开器已安装');

  // 设置侧边栏为持久性
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
});

// 监听标签页更新，确保侧边栏在新标签页中也可用
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 确保侧边栏在所有标签页中都可用
    chrome.sidePanel.setOptions({
      tabId: tabId,
      path: 'popup.html',
      enabled: true
    });
  }
});
