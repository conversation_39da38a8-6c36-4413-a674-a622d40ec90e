body {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
    line-height: 1.5;
    font-size: 14px;
}

.container {
    padding: 16px;
    max-width: 100%;
    background: #ffffff;
    min-height: 100vh;
}

h2 {
    margin: 0 0 20px 0;
    color: #333333;
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
}

h2::before {
    content: "🔗";
    font-size: 16px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: #ffffff;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    text-align: center;
    position: relative;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-btn.active {
    border-bottom-color: #4285f4;
}

.tab-title {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 2px;
}

.tab-btn.active .tab-title {
    color: #4285f4;
}

.tab-subtitle {
    display: block;
    font-size: 12px;
    color: #666666;
    font-weight: 400;
}

.tab-btn.active .tab-subtitle {
    color: #4285f4;
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

p {
    margin: 0 0 16px 0;
    color: #666666;
    font-size: 13px;
    text-align: left;
    font-weight: 400;
    line-height: 1.4;
}

#urlInput {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    resize: vertical;
    box-sizing: border-box;
    background: #ffffff;
    transition: border-color 0.2s ease;
    line-height: 1.4;
}

#urlInput:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

.button-group {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.btn {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
}

.btn:hover {
    background-color: #f8f9fa;
}

.btn-primary {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
}

.btn-primary:hover {
    background: #3367d6;
    border-color: #3367d6;
}

.btn-secondary {
    background: #ffffff;
    color: #666666;
    border-color: #d1d5db;
}

.btn-secondary:hover {
    background: #f8f9fa;
    border-color: #9ca3af;
}

.status {
    margin-top: 16px;
    padding: 12px 16px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
    min-height: 16px;
    font-weight: 400;
    transition: all 0.2s ease;
}

.status.success {
    background: #f0f9ff;
    color: #059669;
    border: 1px solid #bfdbfe;
}

.status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.status.info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* 文本处理区域 */

#textInput {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    resize: vertical;
    box-sizing: border-box;
    margin-bottom: 16px;
    background: #ffffff;
    transition: border-color 0.2s ease;
    line-height: 1.4;
}

#textInput:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 1px #4285f4;
}

/* 处理结果区域 */
.processed-result {
    margin-top: 20px;
    padding: 16px;
    background: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    display: none;
}

.processed-result.show {
    display: block;
}

.processed-result h4 {
    margin: 0 0 12px 0;
    color: #333333;
    font-size: 14px;
    font-weight: 600;
}

.result-content {
    max-height: 300px;
    overflow-y: auto;
}

/* 词条列表样式 */
.word-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #ffffff;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.word-item:hover {
    background: #f8f9fa;
    border-color: #e5e5e5;
}

.word-text {
    flex: 1;
    font-size: 14px;
    color: #333333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin-right: 12px;
    font-weight: 400;
}

.word-actions {
    display: flex;
    gap: 6px;
}

.action-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    background: #ffffff;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #9ca3af;
}

.copy-btn {
    color: #4285f4;
}

.copy-btn:hover {
    background: #e8f0fe;
    border-color: #4285f4;
}

.delete-btn {
    color: #dc2626;
}

.delete-btn:hover {
    background: #fef2f2;
    border-color: #dc2626;
}

/* 空状态 */
.empty-result {
    text-align: center;
    color: #666666;
    font-style: normal;
    padding: 24px 16px;
    font-size: 14px;
    font-weight: 400;
}