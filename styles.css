body {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #1d1d1f;
    line-height: 1.6;
}

.container {
    padding: 24px;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: 0;
    box-shadow: none;
    min-height: 100vh;
}

h2 {
    margin: 0 0 32px 0;
    color: #1d1d1f;
    font-size: 28px;
    font-weight: 600;
    text-align: center;
    letter-spacing: -0.5px;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.tab-btn {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
    text-align: center;
    position: relative;
}

.tab-btn:hover {
    background-color: rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
    transform: translateY(-2px);
}

.tab-title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 4px;
    letter-spacing: -0.2px;
}

.tab-btn.active .tab-title {
    color: #ffffff;
}

.tab-subtitle {
    display: block;
    font-size: 12px;
    color: #86868b;
    font-weight: 400;
    opacity: 0.8;
}

.tab-btn.active .tab-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

p {
    margin: 0 0 24px 0;
    color: #86868b;
    font-size: 15px;
    text-align: center;
    font-weight: 400;
    line-height: 1.5;
}

#urlInput {
    width: 100%;
    min-height: 180px;
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    font-size: 15px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    resize: vertical;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    line-height: 1.6;
}

#urlInput:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

.button-group {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

.btn {
    flex: 1;
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    letter-spacing: -0.2px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn:hover::before {
    opacity: 1;
}

.btn-primary {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #FF3B30 0%, #FF9500 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(255, 59, 48, 0.4);
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 59, 48, 0.3);
}

.status {
    margin-top: 24px;
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 14px;
    text-align: center;
    min-height: 20px;
    font-weight: 500;
    letter-spacing: -0.1px;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.status.success {
    background: rgba(52, 199, 89, 0.1);
    color: #30d158;
    border: 1px solid rgba(52, 199, 89, 0.2);
    box-shadow: 0 4px 20px rgba(52, 199, 89, 0.1);
}

.status.error {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    border: 1px solid rgba(255, 59, 48, 0.2);
    box-shadow: 0 4px 20px rgba(255, 59, 48, 0.1);
}

.status.info {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
    border: 1px solid rgba(0, 122, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.1);
}

/* 文本处理区域 */

#textInput {
    width: 100%;
    min-height: 140px;
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    font-size: 15px;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    resize: vertical;
    box-sizing: border-box;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    line-height: 1.6;
}

#textInput:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

/* 处理结果区域 */
.processed-result {
    margin-top: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 20px;
    display: none;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.processed-result.show {
    display: block;
    animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.processed-result h4 {
    margin: 0 0 20px 0;
    color: #1d1d1f;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: -0.3px;
}

.result-content {
    max-height: 300px;
    overflow-y: auto;
}

/* 词条列表样式 */
.word-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 12px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.word-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(0, 122, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.word-text {
    flex: 1;
    font-size: 16px;
    color: #1d1d1f;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
    margin-right: 16px;
    font-weight: 500;
    letter-spacing: -0.1px;
}

.word-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-btn:hover::before {
    opacity: 1;
}

.copy-btn {
    background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

.copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 122, 255, 0.4);
}

.delete-btn {
    background: linear-gradient(135deg, #FF3B30 0%, #FF9500 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3);
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 59, 48, 0.4);
}

.action-btn:active {
    transform: translateY(0);
}

/* 空状态 */
.empty-result {
    text-align: center;
    color: #86868b;
    font-style: normal;
    padding: 40px 20px;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.2px;
}