<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接批量打开器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h2>链接批量打开器</h2>
        <p>请粘贴从Excel复制的链接地址（每行一个）</p>
        
        <textarea 
            id="urlInput" 
            placeholder="在此粘贴链接地址...
https://example1.com
https://example2.com
https://example3.com"
            rows="8"
            cols="50"
        ></textarea>
        
        <div class="button-group">
            <button id="openAllBtn" class="btn btn-primary">打开全部链接</button>
            <button id="clearBtn" class="btn btn-secondary">清理内容</button>
        </div>

        <div class="separator"></div>

        <h3>文本处理工具</h3>
        <p>粘贴Excel列内容，自动处理"-"符号并过滤纯数字</p>

        <textarea
            id="textInput"
            placeholder="在此粘贴Excel列内容...
例如：
word-one
word-two
12345
text-content"
            rows="6"
            cols="50"
        ></textarea>

        <div class="button-group">
            <button id="processTextBtn" class="btn btn-primary">处理文本</button>
            <button id="clearTextBtn" class="btn btn-secondary">清理文本</button>
        </div>

        <div id="processedResult" class="processed-result">
            <h4>处理结果：</h4>
            <div id="resultContent" class="result-content"></div>
        </div>

        <div id="status" class="status"></div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>