document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('urlInput');
    const openAllBtn = document.getElementById('openAllBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');

    // 文本处理相关元素
    const textInput = document.getElementById('textInput');
    const processTextBtn = document.getElementById('processTextBtn');
    const clearTextBtn = document.getElementById('clearTextBtn');
    const processedResult = document.getElementById('processedResult');
    const resultContent = document.getElementById('resultContent');

    // 加载保存的URL和文本
    chrome.storage.local.get(['savedUrls', 'savedText'], function(result) {
        if (result.savedUrls) {
            urlInput.value = result.savedUrls;
        }
        if (result.savedText) {
            textInput.value = result.savedText;
        }
    });

    // 打开全部链接
    openAllBtn.addEventListener('click', async function() {
        const inputText = urlInput.value.trim();
        
        if (!inputText) {
            showStatus('请输入链接地址', 'error');
            return;
        }

        // 解析URL
        const urls = parseUrls(inputText);
        
        if (urls.length === 0) {
            showStatus('未找到有效的链接地址', 'error');
            return;
        }

        showStatus(`正在打开 ${urls.length} 个链接...`, 'info');

        // 保存当前输入
        chrome.storage.local.set({savedUrls: inputText});

        // 批量打开链接
        let openedCount = 0;
        for (const url of urls) {
            try {
                // 添加延迟避免被浏览器阻止
                await new Promise(resolve => setTimeout(resolve, 100));
                chrome.tabs.create({ url: url });
                openedCount++;
            } catch (error) {
                console.error('打开链接失败:', url, error);
            }
        }

        showStatus(`成功打开 ${openedCount} 个链接`, 'success');
        
        // 2秒后关闭弹窗
        setTimeout(() => {
            window.close();
        }, 2000);
    });

    // 清理内容
    clearBtn.addEventListener('click', function() {
        urlInput.value = '';
        chrome.storage.local.remove(['savedUrls']);
        showStatus('内容已清理', 'info');
        
        // 聚焦到输入框
        urlInput.focus();
    });

    // 解析URL函数
    function parseUrls(text) {
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        
        const urls = [];
        const urlRegex = /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/\S*)?$/i;
        
        for (const line of lines) {
            let url = line.trim();
            
            // 如果没有http前缀，添加https://
            if (!url.startsWith('http')) {
                if (url.startsWith('www.')) {
                    url = 'https://' + url;
                } else {
                    url = 'https://' + url;
                }
            }
            
            // 验证URL格式
            if (urlRegex.test(url)) {
                urls.push(url);
            }
        }
        
        return urls;
    }

    // 显示状态信息
    function showStatus(message, type) {
        status.textContent = message;
        status.className = 'status ' + type;
        
        // 3秒后自动清除状态信息
        setTimeout(() => {
            status.textContent = '';
            status.className = 'status';
        }, 3000);
    }

    // 监听Ctrl+Enter快捷键
    urlInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            openAllBtn.click();
        }
    });

    // 文本处理功能
    processTextBtn.addEventListener('click', function() {
        const inputText = textInput.value.trim();

        if (!inputText) {
            showStatus('请输入要处理的文本', 'error');
            return;
        }

        // 保存当前输入
        chrome.storage.local.set({savedText: inputText});

        // 处理文本
        const processedText = processText(inputText);

        if (processedText.length === 0) {
            showStatus('处理后没有有效内容', 'info');
            resultContent.textContent = '无有效内容';
        } else {
            showStatus(`成功处理 ${processedText.length} 个词条`, 'success');
            resultContent.textContent = processedText.join('\n');
        }

        processedResult.classList.add('show');
    });

    // 清理文本
    clearTextBtn.addEventListener('click', function() {
        textInput.value = '';
        chrome.storage.local.remove(['savedText']);
        processedResult.classList.remove('show');
        showStatus('文本内容已清理', 'info');

        // 聚焦到输入框
        textInput.focus();
    });

    // 文本处理函数
    function processText(text) {
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        const processedLines = [];

        for (const line of lines) {
            // 检查是否全部是数字（包括小数点）
            if (/^\d+\.?\d*$/.test(line)) {
                // 全部是数字，跳过
                continue;
            }

            // 将"-"替换为空格
            const processed = line.replace(/-/g, ' ');
            processedLines.push(processed);
        }

        return processedLines;
    }

    // 为文本输入框添加快捷键支持
    textInput.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            processTextBtn.click();
        }
    });
});